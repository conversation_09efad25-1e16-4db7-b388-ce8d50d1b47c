#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化后的邮件同步机制
验证只依靠mail_hash进行重复检测和减少fetch调用的效果
"""

import sys
import os
import time
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db import Database
from utils.email.mail_precheck import MailPreChecker
from utils.email.common import generate_mail_hash

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_mail_hash_generation():
    """测试邮件哈希生成"""
    print("\n" + "=" * 60)
    print("测试邮件哈希生成")
    print("=" * 60)
    
    try:
        # 测试数据
        test_mails = [
            {
                'subject': '测试邮件1',
                'sender': '<EMAIL>',
                'received_time': datetime.now(),
                'content_preview': '这是测试邮件1的内容'
            },
            {
                'subject': '测试邮件2',
                'sender': '<EMAIL>',
                'received_time': datetime.now() - timedelta(minutes=1),
                'content_preview': '这是测试邮件2的内容'
            },
            {
                'subject': '测试邮件1',  # 重复邮件
                'sender': '<EMAIL>',
                'received_time': datetime.now(),
                'content_preview': '这是测试邮件1的内容'
            }
        ]
        
        hashes = []
        for i, mail in enumerate(test_mails):
            mail_hash = generate_mail_hash(
                mail['subject'],
                mail['sender'],
                mail['received_time'],
                mail['content_preview']
            )
            hashes.append(mail_hash)
            print(f"邮件{i+1}: {mail['subject']} -> {mail_hash[:16]}...")
        
        # 检查重复
        if hashes[0] == hashes[2]:
            print("✓ 重复邮件生成了相同的哈希值")
        else:
            print("✗ 重复邮件生成了不同的哈希值")
            
        if hashes[0] != hashes[1]:
            print("✓ 不同邮件生成了不同的哈希值")
        else:
            print("✗ 不同邮件生成了相同的哈希值")
            
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False

def test_database_functions():
    """测试数据库检查函数"""
    print("\n" + "=" * 60)
    print("测试数据库检查函数")
    print("=" * 60)
    
    try:
        db = Database()
        
        # 测试邮件数据
        test_email_id = 1
        test_mail = {
            'subject': '数据库测试邮件',
            'sender': '<EMAIL>',
            'received_time': datetime.now(),
            'content': '这是数据库测试邮件的内容'
        }
        
        # 生成哈希
        mail_hash = generate_mail_hash(
            test_mail['subject'],
            test_mail['sender'],
            test_mail['received_time'],
            test_mail['content'][:100]
        )
        
        print(f"测试邮件哈希: {mail_hash[:16]}...")
        
        # 1. 测试check_mail_exists函数
        print("\n1. 测试check_mail_exists函数")
        exists_before = db.check_mail_exists(test_email_id, None, None, mail_hash)
        print(f"添加前邮件是否存在: {exists_before}")
        
        # 2. 添加邮件记录
        print("\n2. 添加邮件记录")
        success, mail_id = db.add_mail_record(
            email_id=test_email_id,
            subject=test_mail['subject'],
            sender=test_mail['sender'],
            received_time=test_mail['received_time'],
            content=test_mail['content']
        )
        print(f"邮件添加结果: success={success}, mail_id={mail_id}")
        
        # 3. 再次检查邮件是否存在
        print("\n3. 再次检查邮件是否存在")
        exists_after = db.check_mail_exists(test_email_id, None, None, mail_hash)
        print(f"添加后邮件是否存在: {exists_after}")
        
        # 4. 测试batch_check_mails_exist函数
        print("\n4. 测试batch_check_mails_exist函数")
        mail_identifiers = {
            'mail_hashes': [mail_hash, 'nonexistent_hash_123']
        }
        existing_identifiers = db.batch_check_mails_exist(test_email_id, mail_identifiers)
        print(f"批量检查结果: {existing_identifiers}")
        
        # 验证结果
        if not exists_before and exists_after:
            print("✓ check_mail_exists函数工作正常")
        else:
            print("✗ check_mail_exists函数工作异常")
            
        if ('mail_hash', mail_hash) in existing_identifiers:
            print("✓ batch_check_mails_exist函数工作正常")
        else:
            print("✗ batch_check_mails_exist函数工作异常")
            
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_precheck_mechanism():
    """测试预检查机制"""
    print("\n" + "=" * 60)
    print("测试预检查机制")
    print("=" * 60)
    
    try:
        db = Database()
        precheck = MailPreChecker(db)
        test_email_id = 1
        
        # 准备测试邮件列表
        test_mails = []
        for i in range(5):
            test_mails.append({
                'subject': f'预检查测试邮件 {i}',
                'sender': f'precheck{i}@example.com',
                'received_time': datetime.now() - timedelta(minutes=i),
                'content_preview': f'这是预检查测试邮件{i}的内容'
            })
        
        print(f"准备测试邮件: {len(test_mails)} 封")
        
        # 1. 测试批量检查
        print("\n1. 测试批量检查")
        start_time = time.time()
        existing_identifiers = precheck.batch_check_mails_exist(test_email_id, test_mails)
        end_time = time.time()
        
        print(f"批量检查耗时: {end_time - start_time:.3f} 秒")
        print(f"已存在邮件数: {len(existing_identifiers)}")
        
        # 2. 测试过滤新邮件
        print("\n2. 测试过滤新邮件")
        start_time = time.time()
        new_mails = precheck.filter_new_mails(test_email_id, test_mails)
        end_time = time.time()
        
        print(f"过滤耗时: {end_time - start_time:.3f} 秒")
        print(f"新邮件数: {len(new_mails)}")
        print(f"过滤效率: {len(new_mails)}/{len(test_mails)} = {len(new_mails)/len(test_mails)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """测试性能对比"""
    print("\n" + "=" * 60)
    print("测试性能对比")
    print("=" * 60)
    
    try:
        db = Database()
        
        # 生成大量测试邮件
        test_mails = []
        for i in range(100):
            test_mails.append({
                'subject': f'性能测试邮件 {i}',
                'sender': f'perf{i}@example.com',
                'received_time': datetime.now() - timedelta(minutes=i),
                'content_preview': f'这是性能测试邮件{i}的内容'
            })
        
        print(f"生成测试邮件: {len(test_mails)} 封")
        
        # 测试只使用mail_hash的性能
        precheck = MailPreChecker(db)
        
        start_time = time.time()
        new_mails = precheck.filter_new_mails(1, test_mails)
        end_time = time.time()
        
        print(f"优化后检查耗时: {end_time - start_time:.3f} 秒")
        print(f"平均每封邮件检查耗时: {(end_time - start_time) / len(test_mails) * 1000:.2f} 毫秒")
        print(f"新邮件数: {len(new_mails)}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试优化后的邮件同步机制")
    print("=" * 80)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("邮件哈希生成", test_mail_hash_generation()))
    test_results.append(("数据库检查函数", test_database_functions()))
    test_results.append(("预检查机制", test_precheck_mechanism()))
    test_results.append(("性能对比", test_performance_comparison()))
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！邮件同步机制优化成功。")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
