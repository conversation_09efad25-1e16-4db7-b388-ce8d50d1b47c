#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
邮件同步机制性能对比测试
对比优化前后的性能差异
"""

import sys
import os
import time
import random
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db import Database
from utils.email.mail_precheck import MailPreChecker
from utils.email.common import generate_mail_hash

def simulate_old_sync_method(db, email_id, mail_list):
    """模拟优化前的同步方法：每封邮件都需要获取完整内容"""
    print("模拟优化前的同步方法...")
    
    start_time = time.time()
    processed_count = 0
    fetch_count = 0  # 模拟fetch调用次数
    
    for mail in mail_list:
        # 模拟获取完整邮件内容（每封邮件都需要fetch）
        fetch_count += 1
        time.sleep(0.001)  # 模拟网络延迟
        
        # 检查是否重复（只能用mail_hash）
        mail_hash = generate_mail_hash(
            mail['subject'], 
            mail['sender'], 
            mail['received_time']
        )
        
        exists = db.check_mail_exists(email_id, mail_hash=mail_hash)
        if not exists:
            # 模拟保存邮件
            success, _ = db.add_mail_record(
                email_id=email_id,
                subject=mail['subject'],
                sender=mail['sender'],
                received_time=mail['received_time'],
                content=mail['content']
            )
            if success:
                processed_count += 1
    
    end_time = time.time()
    total_time = end_time - start_time
    
    return {
        'total_time': total_time,
        'processed_count': processed_count,
        'fetch_count': fetch_count,
        'avg_time_per_mail': total_time / len(mail_list) if mail_list else 0
    }

def simulate_new_sync_method(db, email_id, mail_list):
    """模拟优化后的同步方法：三阶段处理"""
    print("模拟优化后的同步方法...")
    
    start_time = time.time()
    
    # 第一阶段：批量获取邮件头信息（模拟批量fetch header）
    phase1_start = time.time()
    header_fetch_count = 1  # 批量获取，只需要一次fetch
    time.sleep(0.01)  # 模拟批量获取头信息的时间
    
    # 为每封邮件添加message_id和server_uid（保持已存在邮件的标识符）
    for i, mail in enumerate(mail_list):
        if 'message_id' not in mail:  # 如果还没有标识符，生成新的
            mail['message_id'] = f"<msg{i}@example.com>"
            mail['server_uid'] = f"{1000 + i}"
    
    phase1_time = time.time() - phase1_start
    
    # 第二阶段：预检查过滤
    phase2_start = time.time()
    precheck = MailPreChecker(db)
    new_mails = precheck.filter_new_mails(email_id, mail_list)
    phase2_time = time.time() - phase2_start
    
    # 第三阶段：只对新邮件获取完整内容
    phase3_start = time.time()
    content_fetch_count = len(new_mails)  # 只对新邮件fetch完整内容
    processed_count = 0
    
    for mail in new_mails:
        # 模拟获取完整邮件内容
        time.sleep(0.001)  # 模拟网络延迟
        
        # 保存邮件（已经过预检查，直接保存）
        success, _ = db.add_mail_record(
            email_id=email_id,
            subject=mail['subject'],
            sender=mail['sender'],
            received_time=mail['received_time'],
            content=mail['content'],
            message_id=mail['message_id'],
            server_uid=mail['server_uid']
        )
        if success:
            processed_count += 1
    
    phase3_time = time.time() - phase3_start
    end_time = time.time()
    total_time = end_time - start_time
    
    return {
        'total_time': total_time,
        'processed_count': processed_count,
        'fetch_count': header_fetch_count + content_fetch_count,
        'avg_time_per_mail': total_time / len(mail_list) if mail_list else 0,
        'phase1_time': phase1_time,
        'phase2_time': phase2_time,
        'phase3_time': phase3_time,
        'new_mails_count': len(new_mails),
        'skipped_count': len(mail_list) - len(new_mails)
    }

def generate_test_mails(count, existing_ratio=0.3):
    """生成测试邮件数据"""
    mails = []
    
    # 生成一些已存在的邮件
    existing_count = int(count * existing_ratio)
    for i in range(existing_count):
        mails.append({
            'subject': f'已存在邮件{i}',
            'sender': f'existing{i}@example.com',
            'received_time': datetime.now(),
            'content': f'这是已存在的邮件{i}的内容'
        })
    
    # 生成新邮件
    new_count = count - existing_count
    for i in range(new_count):
        mails.append({
            'subject': f'新邮件{i}',
            'sender': f'new{i}@example.com',
            'received_time': datetime.now(),
            'content': f'这是新邮件{i}的内容'
        })
    
    # 随机打乱顺序
    random.shuffle(mails)
    return mails

def run_performance_comparison():
    """运行性能对比测试"""
    print("=" * 80)
    print("邮件同步机制性能对比测试")
    print("=" * 80)
    
    db = Database()
    test_email_id = 1
    
    # 测试不同规模的邮件数量
    test_sizes = [50, 100, 200]
    
    for size in test_sizes:
        print(f"\n{'='*60}")
        print(f"测试规模: {size} 封邮件")
        print(f"{'='*60}")
        
        # 生成测试数据
        test_mails = generate_test_mails(size, existing_ratio=0.4)
        
        # 预先插入一些已存在的邮件
        print("预先插入已存在邮件...")
        existing_mails = [mail for mail in test_mails if '已存在' in mail['subject']]
        for mail in existing_mails:
            # 为已存在邮件添加标识符
            mail['message_id'] = f"<existing_{mail['subject'].replace('已存在邮件', '')}@example.com>"
            mail['server_uid'] = f"existing_{mail['subject'].replace('已存在邮件', '')}"

            db.add_mail_record(
                email_id=test_email_id,
                subject=mail['subject'],
                sender=mail['sender'],
                received_time=mail['received_time'],
                content=mail['content'],
                message_id=mail['message_id'],
                server_uid=mail['server_uid']
            )
        
        print(f"预插入 {len(existing_mails)} 封已存在邮件")
        
        # 测试优化前的方法
        print(f"\n--- 优化前的同步方法 ---")
        old_result = simulate_old_sync_method(db, test_email_id, test_mails)
        
        print(f"总耗时: {old_result['total_time']:.3f}秒")
        print(f"处理邮件数: {old_result['processed_count']}")
        print(f"Fetch调用次数: {old_result['fetch_count']}")
        print(f"平均每封邮件耗时: {old_result['avg_time_per_mail']*1000:.2f}ms")
        
        # 清理数据，准备测试新方法
        db.conn.execute("DELETE FROM mail_records WHERE email_id = ?", (test_email_id,))
        db.conn.commit()
        
        # 重新插入已存在邮件
        for mail in existing_mails:
            db.add_mail_record(
                email_id=test_email_id,
                subject=mail['subject'],
                sender=mail['sender'],
                received_time=mail['received_time'],
                content=mail['content'],
                message_id=mail['message_id'],
                server_uid=mail['server_uid']
            )
        
        # 测试优化后的方法
        print(f"\n--- 优化后的同步方法 ---")
        new_result = simulate_new_sync_method(db, test_email_id, test_mails)
        
        print(f"总耗时: {new_result['total_time']:.3f}秒")
        print(f"  - 第一阶段（批量获取头信息）: {new_result['phase1_time']:.3f}秒")
        print(f"  - 第二阶段（预检查过滤）: {new_result['phase2_time']:.3f}秒")
        print(f"  - 第三阶段（获取新邮件内容）: {new_result['phase3_time']:.3f}秒")
        print(f"处理邮件数: {new_result['processed_count']}")
        print(f"新邮件数: {new_result['new_mails_count']}")
        print(f"跳过邮件数: {new_result['skipped_count']}")
        print(f"Fetch调用次数: {new_result['fetch_count']}")
        print(f"平均每封邮件耗时: {new_result['avg_time_per_mail']*1000:.2f}ms")
        
        # 性能对比
        print(f"\n--- 性能对比 ---")
        time_improvement = (old_result['total_time'] - new_result['total_time']) / old_result['total_time'] * 100
        fetch_reduction = (old_result['fetch_count'] - new_result['fetch_count']) / old_result['fetch_count'] * 100
        
        print(f"时间优化: {time_improvement:.1f}%")
        print(f"Fetch调用减少: {fetch_reduction:.1f}%")
        print(f"避免获取完整内容的邮件: {new_result['skipped_count']} 封")
        
        # 清理测试数据
        db.conn.execute("DELETE FROM mail_records WHERE email_id = ?", (test_email_id,))
        db.conn.commit()

def main():
    """主函数"""
    print("邮件同步机制优化 - 性能对比测试")
    print("对比批量获取message_id优化前后的性能差异")
    
    try:
        run_performance_comparison()
        
        print(f"\n{'='*80}")
        print("性能对比测试完成！")
        print("优化要点:")
        print("1. 批量获取邮件头信息，减少网络请求")
        print("2. 使用Message-ID和Server-UID进行精确重复检测")
        print("3. 预检查过滤，只对新邮件获取完整内容")
        print("4. 多层标识符体系，提高检测准确性")
        print(f"{'='*80}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
