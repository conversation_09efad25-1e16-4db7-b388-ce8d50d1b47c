#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试数据库连接消息修复
验证不再出现"未提供数据库连接，将处理所有邮件"的提示
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db import Database
from utils.email.imap import IMAPMailHandler
from utils.email.outlook import OutlookMailHandler

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imap_db_connection_message():
    """测试IMAP处理器不再显示数据库连接消息"""
    print("测试IMAP处理器数据库连接消息...")
    
    try:
        db = Database()
        
        # 捕获日志输出
        import io
        import contextlib
        
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        handler.setLevel(logging.INFO)
        
        # 获取IMAP logger
        imap_logger = logging.getLogger('utils.email.imap')
        imap_logger.addHandler(handler)
        
        try:
            # 调用fetch_emails方法，传递db和email_id参数
            result = IMAPMailHandler.fetch_emails(
                email_address="<EMAIL>",
                password="test_password",
                server="imap.gmail.com",
                port=993,
                use_ssl=True,
                folder="INBOX",
                callback=lambda p, m: None,
                last_check_time=None,
                db=db,
                email_id=1
            )
        except Exception as e:
            # 预期会失败，但我们关心的是日志消息
            pass
        
        # 检查日志输出
        log_output = log_capture.getvalue()
        
        if "未提供数据库连接，将处理所有邮件" in log_output:
            print("✗ 仍然出现数据库连接提示消息")
            print(f"日志输出: {log_output}")
            return False
        else:
            print("✓ 不再出现数据库连接提示消息")
            return True
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False

def test_outlook_db_connection_message():
    """测试Outlook处理器不再显示数据库连接消息"""
    print("测试Outlook处理器数据库连接消息...")
    
    try:
        db = Database()
        
        # 捕获日志输出
        import io
        import contextlib
        
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        handler.setLevel(logging.INFO)
        
        # 获取Outlook logger
        outlook_logger = logging.getLogger('utils.email.outlook')
        outlook_logger.addHandler(handler)
        
        try:
            # 调用fetch_emails方法，传递db和email_id参数
            result = OutlookMailHandler.fetch_emails(
                email_address="<EMAIL>",
                access_token="fake_token",
                folder="inbox",
                callback=lambda p, m: None,
                last_check_time=None,
                db=db,
                email_id=1
            )
        except Exception as e:
            # 预期会失败，但我们关心的是日志消息
            pass
        
        # 检查日志输出
        log_output = log_capture.getvalue()
        
        if "未提供数据库连接，将处理所有邮件" in log_output:
            print("✗ 仍然出现数据库连接提示消息")
            print(f"日志输出: {log_output}")
            return False
        else:
            print("✓ 不再出现数据库连接提示消息")
            return True
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("数据库连接消息修复验证测试")
    print("验证不再出现'未提供数据库连接，将处理所有邮件'的提示")
    print("=" * 80)
    
    tests = [
        test_imap_db_connection_message,
        test_outlook_db_connection_message
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("=" * 80)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据库连接消息问题已修复。")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    main()
