#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一邮箱连接诊断工具
支持所有邮箱类型：IMAP、Outlook、Gmail、QQ、Yahoo等
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.email.imap import IMAPMailHandler
from utils.email.outlook import OutlookMailHandler
from utils.email.gmail import GmailHandler
from utils.email.qq import QQMailHandler
from utils.email.yahoo import YahooMailHandler
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UniversalMailDiagnostics:
    """统一邮箱连接诊断类"""
    
    def __init__(self):
        # 邮箱类型配置
        self.mail_configs = {
            'gmail': {
                'handler': <PERSON>mail<PERSON>and<PERSON>,
                'server': 'imap.gmail.com',
                'port': 993,
                'use_ssl': True,
                'description': 'Gmail邮箱',
                'auth_type': 'password',
                'notes': '需要应用专用密码或开启不够安全的应用访问权限'
            },
            'qq': {
                'handler': QQMailHandler,
                'server': 'imap.qq.com',
                'port': 993,
                'use_ssl': True,
                'description': 'QQ邮箱',
                'auth_type': 'password',
                'notes': '需要开启IMAP服务并使用授权码'
            },
            'outlook': {
                'handler': OutlookMailHandler,
                'server': 'outlook.office365.com',
                'port': 993,
                'use_ssl': True,
                'description': 'Outlook/Hotmail邮箱',
                'auth_type': 'oauth2',
                'notes': '需要OAuth2访问令牌或应用专用密码'
            },
            'yahoo': {
                'handler': YahooMailHandler,
                'server': 'imap.mail.yahoo.com',
                'port': 993,
                'use_ssl': True,
                'description': 'Yahoo邮箱',
                'auth_type': 'password',
                'notes': '需要应用专用密码'
            },
            '163': {
                'handler': IMAPMailHandler,
                'server': 'imap.163.com',
                'port': 993,
                'use_ssl': True,
                'description': '163邮箱',
                'auth_type': 'password',
                'notes': '需要开启IMAP服务并使用授权码'
            },
            '126': {
                'handler': IMAPMailHandler,
                'server': 'imap.126.com',
                'port': 993,
                'use_ssl': True,
                'description': '126邮箱',
                'auth_type': 'password',
                'notes': '需要开启IMAP服务并使用授权码'
            },
            'imap': {
                'handler': IMAPMailHandler,
                'server': None,  # 用户自定义
                'port': 993,
                'use_ssl': True,
                'description': '通用IMAP邮箱',
                'auth_type': 'password',
                'notes': '需要正确的IMAP服务器配置'
            }
        }
    
    def detect_mail_type(self, email_address):
        """根据邮箱地址自动检测邮箱类型"""
        if '@' not in email_address:
            return 'imap'
        
        domain = email_address.split('@')[1].lower()
        
        if 'gmail.com' in domain:
            return 'gmail'
        elif 'qq.com' in domain:
            return 'qq'
        elif 'outlook.com' in domain or 'hotmail.com' in domain or 'live.com' in domain:
            return 'outlook'
        elif 'yahoo.com' in domain:
            return 'yahoo'
        elif '163.com' in domain:
            return '163'
        elif '126.com' in domain:
            return '126'
        else:
            return 'imap'
    
    def diagnose_connection(self, mail_type, email_address, password, server=None, port=None, access_token=None):
        """诊断邮箱连接"""
        print("=" * 80)
        print(f"开始诊断 {self.mail_configs[mail_type]['description']} 连接")
        print("=" * 80)
        
        config = self.mail_configs[mail_type]
        
        # 使用配置或用户提供的参数
        server = server or config['server']
        port = port or config['port']
        use_ssl = config['use_ssl']
        
        print(f"邮箱类型: {config['description']}")
        print(f"服务器: {server}:{port}")
        print(f"SSL: {use_ssl}")
        print(f"邮箱地址: {email_address}")
        print(f"认证类型: {config['auth_type']}")
        print(f"特殊说明: {config['notes']}")
        
        try:
            # 创建处理器
            if mail_type == 'outlook' and access_token:
                # Outlook使用OAuth2
                handler = OutlookMailHandler(email_address, access_token)
            elif mail_type in ['gmail', 'qq', 'yahoo']:
                # 使用专用处理器
                handler = config['handler'](email_address, password)
            else:
                # 使用通用IMAP处理器
                handler = IMAPMailHandler(server, email_address, password, use_ssl, port)
            
            # 尝试连接
            print(f"\n正在连接...")
            success = handler.connect()
            
            if success:
                print("✓ 连接成功！")
                
                # 测试基本功能
                print("\n测试基本功能...")
                try:
                    if hasattr(handler, 'get_folders'):
                        folders = handler.get_folders()
                        print(f"✓ 成功获取 {len(folders)} 个文件夹")
                    
                    if hasattr(handler, 'mail') and handler.mail:
                        handler.mail.select('INBOX')
                        _, messages = handler.mail.search(None, 'ALL')
                        message_count = len(messages[0].split()) if messages[0] else 0
                        print(f"✓ 收件箱中有 {message_count} 封邮件")
                        
                except Exception as e:
                    print(f"⚠ 基本功能测试失败: {e}")
                
                # 关闭连接
                handler.close()
                
                print(f"\n{'='*80}")
                print("✓ 诊断成功！邮箱连接正常。")
                print(f"{'='*80}")
                return True
                
            else:
                print(f"✗ 连接失败: {handler.error}")
                
                print(f"\n{'='*80}")
                print("✗ 诊断失败！请根据错误信息和建议进行调整。")
                print(f"{'='*80}")
                return False
                
        except Exception as e:
            print(f"✗ 诊断过程中出现异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def batch_diagnose(self, test_accounts):
        """批量诊断多个邮箱账户"""
        print("=" * 80)
        print("批量邮箱连接诊断")
        print("=" * 80)
        
        results = []
        
        for i, account in enumerate(test_accounts, 1):
            print(f"\n[{i}/{len(test_accounts)}] 诊断 {account['email']}")
            print("-" * 60)
            
            mail_type = account.get('type') or self.detect_mail_type(account['email'])
            
            success = self.diagnose_connection(
                mail_type=mail_type,
                email_address=account['email'],
                password=account.get('password', ''),
                server=account.get('server'),
                port=account.get('port'),
                access_token=account.get('access_token')
            )
            
            results.append({
                'email': account['email'],
                'type': mail_type,
                'success': success
            })
        
        # 显示汇总结果
        print(f"\n{'='*80}")
        print("批量诊断结果汇总")
        print(f"{'='*80}")
        
        success_count = sum(1 for r in results if r['success'])
        fail_count = len(results) - success_count
        
        print(f"总计: {len(results)} 个账户")
        print(f"成功: {success_count} 个")
        print(f"失败: {fail_count} 个")
        
        if fail_count > 0:
            print(f"\n失败的账户:")
            for result in results:
                if not result['success']:
                    print(f"  ✗ {result['email']} ({result['type']})")
        
        return results

def interactive_diagnosis():
    """交互式诊断"""
    diagnostics = UniversalMailDiagnostics()
    
    print("统一邮箱连接诊断工具")
    print("支持Gmail、QQ、Outlook、Yahoo、163、126等邮箱")
    
    # 获取邮箱地址
    email_address = input("\n请输入邮箱地址: ").strip()
    
    # 自动检测邮箱类型
    detected_type = diagnostics.detect_mail_type(email_address)
    config = diagnostics.mail_configs[detected_type]
    
    print(f"\n检测到邮箱类型: {config['description']}")
    print(f"建议配置: {config['server']}:{config['port']}")
    print(f"特殊说明: {config['notes']}")
    
    # 确认邮箱类型
    print(f"\n支持的邮箱类型:")
    for key, cfg in diagnostics.mail_configs.items():
        print(f"  {key}: {cfg['description']}")
    
    mail_type = input(f"\n确认邮箱类型 (默认: {detected_type}): ").strip() or detected_type
    
    if mail_type not in diagnostics.mail_configs:
        print(f"不支持的邮箱类型: {mail_type}")
        return
    
    # 获取认证信息
    config = diagnostics.mail_configs[mail_type]
    
    if config['auth_type'] == 'oauth2':
        access_token = input("请输入访问令牌 (Access Token): ").strip()
        password = None
    else:
        password = input("请输入密码/授权码: ").strip()
        access_token = None
    
    # 获取服务器配置（如果需要）
    server = None
    port = None
    
    if mail_type == 'imap':
        server = input("请输入IMAP服务器地址: ").strip()
        port_input = input("请输入端口号 (默认993): ").strip()
        port = int(port_input) if port_input else 993
    
    # 开始诊断
    try:
        diagnostics.diagnose_connection(
            mail_type=mail_type,
            email_address=email_address,
            password=password,
            server=server,
            port=port,
            access_token=access_token
        )
    except KeyboardInterrupt:
        print("\n\n诊断被用户中断")
    except Exception as e:
        print(f"\n\n诊断过程中出现错误: {str(e)}")

def main():
    """主函数"""
    print("统一邮箱连接诊断工具")
    print("支持所有邮箱类型的连接测试和问题诊断")
    
    print("\n请选择诊断模式:")
    print("1. 交互式诊断")
    print("2. 批量诊断")
    print("3. 查看支持的邮箱类型")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    try:
        if choice == '1':
            interactive_diagnosis()
        elif choice == '2':
            print("\n批量诊断功能")
            print("请准备包含邮箱信息的配置文件或手动输入")
            # 这里可以扩展为从文件读取或手动输入多个账户
            print("功能开发中...")
        elif choice == '3':
            diagnostics = UniversalMailDiagnostics()
            print("\n支持的邮箱类型:")
            for key, config in diagnostics.mail_configs.items():
                print(f"  {key}: {config['description']}")
                print(f"    服务器: {config['server'] or '用户自定义'}")
                print(f"    端口: {config['port']}")
                print(f"    认证: {config['auth_type']}")
                print(f"    说明: {config['notes']}")
                print()
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n\n程序运行出现错误: {str(e)}")

if __name__ == "__main__":
    main()
