#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库迁移脚本
添加message_id、server_uid、mail_hash字段到mail_records表
"""

import sys
import os
import sqlite3

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db import Database
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def migrate_database():
    """执行数据库迁移"""
    print("=" * 60)
    print("开始数据库迁移")
    print("=" * 60)
    
    try:
        # 创建数据库实例，这会自动执行初始化和字段检查
        db = Database()
        
        print("✓ 数据库连接成功")
        print("✓ 表结构检查完成")
        print("✓ 新字段添加完成")
        print("✓ 索引创建完成")
        
        # 验证字段是否存在
        cursor = db.conn.execute("PRAGMA table_info(mail_records)")
        columns = [row[1] for row in cursor.fetchall()]
        
        print("\n当前mail_records表字段:")
        for i, column in enumerate(columns, 1):
            print(f"  {i}. {column}")
        
        # 检查新字段
        required_fields = ['message_id', 'server_uid', 'mail_hash']
        missing_fields = [field for field in required_fields if field not in columns]
        
        if missing_fields:
            print(f"\n⚠️  缺少字段: {missing_fields}")
            print("正在手动添加缺少的字段...")
            
            for field in missing_fields:
                try:
                    if field == 'message_id':
                        db.conn.execute("ALTER TABLE mail_records ADD COLUMN message_id TEXT")
                    elif field == 'server_uid':
                        db.conn.execute("ALTER TABLE mail_records ADD COLUMN server_uid TEXT")
                    elif field == 'mail_hash':
                        db.conn.execute("ALTER TABLE mail_records ADD COLUMN mail_hash TEXT")
                    
                    print(f"  ✓ 添加字段: {field}")
                except Exception as e:
                    print(f"  ✗ 添加字段失败 {field}: {str(e)}")
            
            db.conn.commit()
        else:
            print("\n✓ 所有必需字段都已存在")
        
        # 创建索引
        print("\n创建索引...")
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_mail_records_message_id ON mail_records(email_id, message_id)",
            "CREATE INDEX IF NOT EXISTS idx_mail_records_server_uid ON mail_records(email_id, server_uid)",
            "CREATE INDEX IF NOT EXISTS idx_mail_records_mail_hash ON mail_records(email_id, mail_hash)"
        ]
        
        for index_sql in indexes:
            try:
                db.conn.execute(index_sql)
                print(f"  ✓ 索引创建成功")
            except Exception as e:
                print(f"  ✗ 索引创建失败: {str(e)}")
        
        db.conn.commit()
        
        print("\n" + "=" * 60)
        print("数据库迁移完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 数据库迁移失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_migration():
    """测试迁移结果"""
    print("\n" + "=" * 60)
    print("测试迁移结果")
    print("=" * 60)
    
    try:
        db = Database()
        
        # 测试添加邮件记录
        from datetime import datetime
        
        test_mail = {
            'email_id': 1,
            'subject': '迁移测试邮件',
            'sender': '<EMAIL>',
            'received_time': datetime.now(),
            'content': '这是一封测试邮件',
            'message_id': '<<EMAIL>>',
            'server_uid': '9999'
        }
        
        print("测试添加邮件记录...")
        success, mail_id = db.add_mail_record(
            email_id=test_mail['email_id'],
            subject=test_mail['subject'],
            sender=test_mail['sender'],
            received_time=test_mail['received_time'],
            content=test_mail['content'],
            message_id=test_mail['message_id'],
            server_uid=test_mail['server_uid']
        )
        
        if success:
            print(f"✓ 邮件记录添加成功 (ID: {mail_id})")
            
            # 测试重复检测
            print("测试重复检测...")
            exists = db.check_mail_exists(
                email_id=test_mail['email_id'],
                message_id=test_mail['message_id']
            )
            print(f"✓ Message-ID重复检测: {'存在' if exists else '不存在'}")
            
            exists = db.check_mail_exists(
                email_id=test_mail['email_id'],
                server_uid=test_mail['server_uid']
            )
            print(f"✓ Server-UID重复检测: {'存在' if exists else '不存在'}")
            
            # 测试批量检测
            print("测试批量检测...")
            mail_identifiers = {
                'message_ids': [test_mail['message_id']],
                'server_uids': [test_mail['server_uid']],
                'mail_hashes': []
            }
            
            existing_identifiers = db.batch_check_mails_exist(test_mail['email_id'], mail_identifiers)
            print(f"✓ 批量检测结果: 找到 {len(existing_identifiers)} 个已存在标识符")
            
            # 清理测试数据
            db.conn.execute("DELETE FROM mail_records WHERE id = ?", (mail_id,))
            db.conn.commit()
            print("✓ 测试数据清理完成")
            
        else:
            print("✗ 邮件记录添加失败")
            return False
        
        print("\n✓ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("邮件同步机制优化 - 数据库迁移")
    print("添加message_id、server_uid、mail_hash字段支持")
    
    # 执行迁移
    if migrate_database():
        # 测试迁移结果
        if test_migration():
            print("\n🎉 数据库迁移和测试全部完成！")
            print("现在可以使用优化后的邮件同步机制了。")
        else:
            print("\n⚠️  迁移完成但测试失败，请检查配置。")
    else:
        print("\n❌ 迁移失败，请检查错误信息。")

if __name__ == "__main__":
    main()
