"""
Gmail邮箱处理模块
基于IMAP协议，使用Gmail特定的服务器配置
"""

from .imap import IMAPMailHandler
import imaplib
import email
from email.header import decode_header
from email.utils import parsedate_to_datetime
import os
import logging
from datetime import datetime
from typing import List, Dict, Optional, Callable
from .logger import log_email_start, log_email_complete, log_email_error

logger = logging.getLogger(__name__)

class GmailHandler(IMAPMailHandler):
    """Gmail邮箱处理类"""

    # Gmail IMAP服务器配置
    SERVER = 'imap.gmail.com'
    PORT = 993
    USE_SSL = True

    def __init__(self, username, password, use_ssl=True, port=None):
        """初始化Gmail处理器"""
        super().__init__(self.SERVER, username, password, self.USE_SSL, port or self.PORT)

    def connect(self, max_retries=3, retry_delay=2):
        """连接到Gmail服务器，增强错误处理和重试机制"""
        import time
        import socket
        import ssl
        import imaplib

        for attempt in range(max_retries):
            try:
                logger.info(f"尝试连接Gmail服务器 {self.server}:{self.port} (尝试 {attempt + 1}/{max_retries})")

                # 创建连接
                if self.use_ssl:
                    self.mail = imaplib.IMAP4_SSL(self.server, self.port)
                else:
                    self.mail = imaplib.IMAP4(self.server, self.port)

                logger.info(f"连接建立成功，开始登录: {self.username}")

                # 尝试登录
                try:
                    self.mail.login(self.username, self.password)
                    logger.info("Gmail登录成功")
                    return True

                except imaplib.IMAP4.error as login_error:
                    error_msg = str(login_error).lower()

                    if "server logging out" in error_msg:
                        logger.error(f"Gmail登录被拒绝: {login_error}")
                        self.error = f"登录被拒绝: {login_error}"

                        # 尝试不同的用户名格式
                        if '@' in self.username and attempt == 0:
                            logger.info("尝试使用用户名部分登录Gmail")
                            username_only = self.username.split('@')[0]
                            try:
                                self.mail.login(username_only, self.password)
                                logger.info("Gmail使用用户名部分登录成功")
                                self.username = username_only  # 更新用户名
                                return True
                            except Exception as e2:
                                logger.warning(f"Gmail用户名部分登录也失败: {e2}")

                        # 如果是认证错误，不进行重试
                        if attempt == max_retries - 1:
                            self._provide_gmail_suggestions()
                        break

                    elif "authentication failed" in error_msg or "invalid credentials" in error_msg:
                        logger.error(f"Gmail认证失败: {login_error}")
                        self.error = f"认证失败: {login_error}"
                        self._provide_gmail_suggestions()
                        break

                    else:
                        logger.error(f"Gmail登录错误: {login_error}")
                        self.error = str(login_error)

                # 关闭连接准备重试
                try:
                    self.mail.logout()
                except:
                    pass
                self.mail = None

            except socket.timeout:
                logger.error(f"Gmail连接超时 (尝试 {attempt + 1}/{max_retries})")
                self.error = "连接超时"

            except socket.gaierror as e:
                logger.error(f"Gmail DNS解析失败: {e}")
                self.error = f"无法解析Gmail服务器地址: {self.server}"
                break  # DNS错误不需要重试

            except ConnectionRefusedError:
                logger.error(f"Gmail连接被拒绝: {self.server}:{self.port}")
                self.error = f"Gmail服务器拒绝连接: {self.server}:{self.port}"
                break  # 连接被拒绝不需要重试

            except ssl.SSLError as e:
                logger.error(f"Gmail SSL错误: {e}")
                self.error = f"Gmail SSL连接失败: {e}"
                break  # SSL错误不需要重试

            except Exception as e:
                logger.error(f"Gmail连接失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                self.error = str(e)

            # 如果不是最后一次尝试，等待后重试
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避

        logger.error(f"Gmail所有连接尝试失败，最终错误: {self.error}")
        return False

    def _provide_gmail_suggestions(self):
        """提供Gmail登录问题的解决建议"""
        suggestions = [
            "Gmail需要开启'不够安全的应用的访问权限'或使用应用专用密码",
            "设置地址: https://myaccount.google.com/security",
            "开启两步验证后生成应用专用密码",
            "或者开启'不够安全的应用的访问权限'",
            "确认Gmail账户状态正常",
            "检查用户名格式（可以是完整邮箱地址或用户名部分）"
        ]

        logger.info("Gmail登录失败解决建议:")
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"  {i}. {suggestion}")

        self.error += f"\n解决建议: {'; '.join(suggestions)}"

    @classmethod
    def fetch_emails(cls, email_address, password, folder="INBOX", callback=None, last_check_time=None, db=None, email_id=None):
        """获取Gmail邮箱中的邮件"""
        return super().fetch_emails(
            email_address=email_address,
            password=password,
            server=cls.SERVER,
            port=cls.PORT,
            use_ssl=cls.USE_SSL,
            folder=folder,
            callback=callback,
            last_check_time=last_check_time,
            db=db,
            email_id=email_id
        )

    @classmethod
    def check_mail(cls, email_info, db, progress_callback=None):
        """检查Gmail邮箱的邮件"""
        # 更新邮箱信息为Gmail特定配置
        email_info['server'] = cls.SERVER
        email_info['port'] = cls.PORT
        email_info['use_ssl'] = cls.USE_SSL

        # 调用父类的检查方法
        return super().check_mail(email_info, db, progress_callback)