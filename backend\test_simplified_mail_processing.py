#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简化后的邮件处理流程
验证去除预处理步骤后，邮件处理是否正常工作
"""

import sys
import os
import time
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db import Database
from utils.email.common import generate_mail_hash

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_mail_hash_deduplication():
    """测试邮件哈希去重机制"""
    print("\n" + "=" * 60)
    print("测试邮件哈希去重机制")
    print("=" * 60)
    
    try:
        db = Database()
        test_email_id = 1
        
        # 创建测试邮件数据
        test_mail = {
            'subject': '简化流程测试邮件',
            'sender': '<EMAIL>',
            'received_time': datetime.now(),
            'content': {
                'content': '这是一封测试邮件，用于验证简化后的处理流程',
                'content_type': 'text/plain',
                'has_html': False
            },
            'folder': 'INBOX',
            'has_attachments': 0
        }
        
        # 生成邮件哈希
        content_preview = str(test_mail['content'].get('content', ''))[:100]
        mail_hash = generate_mail_hash(
            test_mail['subject'],
            test_mail['sender'],
            test_mail['received_time'],
            content_preview
        )
        
        print(f"测试邮件信息:")
        print(f"  主题: {test_mail['subject']}")
        print(f"  发件人: {test_mail['sender']}")
        print(f"  时间: {test_mail['received_time']}")
        print(f"  哈希: {mail_hash[:16]}...")
        
        # 1. 检查邮件是否存在（应该不存在）
        print("\n1. 检查邮件是否存在（首次）")
        exists_before = db.check_mail_exists(test_email_id, None, None, mail_hash)
        print(f"邮件是否存在: {exists_before} (预期: False)")
        
        # 2. 添加邮件记录
        print("\n2. 添加邮件记录")
        success, mail_id = db.add_mail_record(
            email_id=test_email_id,
            subject=test_mail['subject'],
            sender=test_mail['sender'],
            received_time=test_mail['received_time'],
            content=test_mail['content'],
            folder=test_mail['folder'],
            has_attachments=test_mail['has_attachments']
        )
        print(f"邮件添加结果: success={success}, mail_id={mail_id}")
        
        # 3. 再次检查邮件是否存在（应该存在）
        print("\n3. 再次检查邮件是否存在")
        exists_after = db.check_mail_exists(test_email_id, None, None, mail_hash)
        print(f"邮件是否存在: {exists_after} (预期: True)")
        
        # 4. 尝试再次添加相同邮件（应该被拒绝）
        print("\n4. 尝试再次添加相同邮件")
        success2, mail_id2 = db.add_mail_record(
            email_id=test_email_id,
            subject=test_mail['subject'],
            sender=test_mail['sender'],
            received_time=test_mail['received_time'],
            content=test_mail['content'],
            folder=test_mail['folder'],
            has_attachments=test_mail['has_attachments']
        )
        print(f"重复添加结果: success={success2}, mail_id={mail_id2} (预期: False, None)")
        
        # 5. 测试批量检查
        print("\n5. 测试批量检查")
        mail_identifiers = {
            'mail_hashes': [mail_hash, 'nonexistent_hash_123']
        }
        existing_identifiers = db.batch_check_mails_exist(test_email_id, mail_identifiers)
        print(f"批量检查结果: {existing_identifiers}")
        
        # 验证结果
        print("\n验证结果:")
        if not exists_before and exists_after:
            print("✓ 邮件哈希检查机制工作正常")
        else:
            print("✗ 邮件哈希检查机制工作异常")
            
        if success and not success2:
            print("✓ 邮件重复添加防护工作正常")
        else:
            print("✗ 邮件重复添加防护工作异常")
            
        if ('mail_hash', mail_hash) in existing_identifiers:
            print("✓ 批量检查机制工作正常")
        else:
            print("✗ 批量检查机制工作异常")
            
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """测试性能对比"""
    print("\n" + "=" * 60)
    print("测试性能对比")
    print("=" * 60)
    
    try:
        db = Database()
        test_email_id = 1
        
        # 生成测试邮件数据
        test_mails = []
        for i in range(50):
            test_mails.append({
                'subject': f'性能测试邮件 {i}',
                'sender': f'perf{i}@test.com',
                'received_time': datetime.now() - timedelta(minutes=i),
                'content': {
                    'content': f'这是性能测试邮件{i}的内容',
                    'content_type': 'text/plain',
                    'has_html': False
                },
                'folder': 'INBOX',
                'has_attachments': 0
            })
        
        print(f"生成测试邮件: {len(test_mails)} 封")
        
        # 测试简化后的处理流程性能
        start_time = time.time()
        
        saved_count = 0
        for mail in test_mails:
            success, mail_id = db.add_mail_record(
                email_id=test_email_id,
                subject=mail['subject'],
                sender=mail['sender'],
                received_time=mail['received_time'],
                content=mail['content'],
                folder=mail['folder'],
                has_attachments=mail['has_attachments']
            )
            if success:
                saved_count += 1
        
        end_time = time.time()
        
        print(f"处理耗时: {end_time - start_time:.3f} 秒")
        print(f"成功保存: {saved_count} 封邮件")
        print(f"平均每封邮件处理耗时: {(end_time - start_time) / len(test_mails) * 1000:.2f} 毫秒")
        
        # 测试重复邮件处理性能
        print("\n测试重复邮件处理性能:")
        start_time = time.time()
        
        duplicate_count = 0
        for mail in test_mails:
            success, mail_id = db.add_mail_record(
                email_id=test_email_id,
                subject=mail['subject'],
                sender=mail['sender'],
                received_time=mail['received_time'],
                content=mail['content'],
                folder=mail['folder'],
                has_attachments=mail['has_attachments']
            )
            if not success:
                duplicate_count += 1
        
        end_time = time.time()
        
        print(f"重复检查耗时: {end_time - start_time:.3f} 秒")
        print(f"检测到重复邮件: {duplicate_count} 封")
        print(f"平均每封重复邮件检查耗时: {(end_time - start_time) / len(test_mails) * 1000:.2f} 毫秒")
        
        return True
        
    except Exception as e:
        print(f"性能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试简化后的邮件处理流程")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("邮件哈希去重机制", test_mail_hash_deduplication),
        ("性能对比测试", test_performance_comparison),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✓ {test_name} 完成")
        except Exception as e:
            print(f"✗ {test_name} 失败: {str(e)}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！简化后的邮件处理流程工作正常。")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
