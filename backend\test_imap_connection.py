#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IMAP连接测试脚本
快速测试和验证IMAP连接配置
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.email.imap import IMAPMailHandler
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imap_connection():
    """测试IMAP连接"""
    print("=" * 60)
    print("IMAP连接测试")
    print("=" * 60)
    
    # 预定义的邮箱配置
    email_configs = {
        'gmail': {
            'server': 'imap.gmail.com',
            'port': 993,
            'use_ssl': True,
            'description': 'Gmail邮箱'
        },
        'qq': {
            'server': 'imap.qq.com',
            'port': 993,
            'use_ssl': True,
            'description': 'QQ邮箱'
        },
        'outlook': {
            'server': 'outlook.office365.com',
            'port': 993,
            'use_ssl': True,
            'description': 'Outlook/Hotmail邮箱'
        },
        '163': {
            'server': 'imap.163.com',
            'port': 993,
            'use_ssl': True,
            'description': '163邮箱'
        },
        '126': {
            'server': 'imap.126.com',
            'port': 993,
            'use_ssl': True,
            'description': '126邮箱'
        }
    }
    
    print("支持的邮箱类型:")
    for key, config in email_configs.items():
        print(f"  {key}: {config['description']} ({config['server']})")
    
    print("\n请选择测试方式:")
    print("1. 使用预定义配置")
    print("2. 自定义配置")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == '1':
        # 使用预定义配置
        email_type = input("请输入邮箱类型 (gmail/qq/outlook/163/126): ").strip().lower()
        
        if email_type not in email_configs:
            print(f"不支持的邮箱类型: {email_type}")
            return
        
        config = email_configs[email_type]
        server = config['server']
        port = config['port']
        use_ssl = config['use_ssl']
        
        print(f"\n使用配置: {config['description']}")
        print(f"服务器: {server}:{port} (SSL: {use_ssl})")
        
    else:
        # 自定义配置
        server = input("请输入IMAP服务器地址: ").strip()
        port = int(input("请输入端口号 (默认993): ").strip() or "993")
        use_ssl_input = input("是否使用SSL (Y/n): ").strip().lower()
        use_ssl = use_ssl_input != 'n'
    
    # 获取邮箱凭据
    email_address = input("请输入邮箱地址: ").strip()
    password = input("请输入密码/授权码: ").strip()
    
    print(f"\n开始测试连接...")
    print(f"服务器: {server}:{port}")
    print(f"SSL: {use_ssl}")
    print(f"邮箱: {email_address}")
    print(f"密码: {'*' * len(password)}")
    
    # 创建IMAP处理器并测试连接
    try:
        handler = IMAPMailHandler(server, email_address, password, use_ssl, port)
        
        print("\n正在连接...")
        success = handler.connect()
        
        if success:
            print("✓ IMAP连接成功！")
            
            # 测试获取文件夹列表
            print("\n测试获取文件夹列表...")
            try:
                folders = handler.get_folders()
                print(f"✓ 成功获取 {len(folders)} 个文件夹:")
                for folder in folders[:10]:  # 只显示前10个
                    print(f"  - {folder}")
                if len(folders) > 10:
                    print(f"  ... 还有 {len(folders) - 10} 个文件夹")
            except Exception as e:
                print(f"⚠ 获取文件夹失败: {e}")
            
            # 测试获取邮件数量
            print("\n测试获取邮件数量...")
            try:
                handler.mail.select('INBOX')
                _, messages = handler.mail.search(None, 'ALL')
                message_count = len(messages[0].split()) if messages[0] else 0
                print(f"✓ 收件箱中有 {message_count} 封邮件")
            except Exception as e:
                print(f"⚠ 获取邮件数量失败: {e}")
            
            # 关闭连接
            handler.close()
            
            print("\n" + "=" * 60)
            print("连接测试成功！您可以使用此配置。")
            print("=" * 60)
            
        else:
            print(f"✗ IMAP连接失败: {handler.error}")
            
            print("\n" + "=" * 60)
            print("连接失败，请检查以下项目:")
            print("1. 邮箱地址和密码是否正确")
            print("2. 是否需要使用应用专用密码或授权码")
            print("3. 是否需要在邮箱设置中开启IMAP服务")
            print("4. 服务器地址和端口是否正确")
            print("5. 网络连接是否正常")
            
            # 根据邮箱类型提供具体建议
            domain = email_address.split('@')[1].lower() if '@' in email_address else ''
            
            if 'gmail' in domain:
                print("\nGmail特殊说明:")
                print("- 需要开启'不够安全的应用的访问权限'")
                print("- 或者使用应用专用密码")
                print("- 设置地址: https://myaccount.google.com/security")
                
            elif 'qq.com' in domain:
                print("\nQQ邮箱特殊说明:")
                print("- 需要在邮箱设置中开启IMAP服务")
                print("- 使用授权码代替QQ密码")
                print("- 设置地址: https://mail.qq.com/")
                
            elif 'outlook' in domain or 'hotmail' in domain or 'live' in domain:
                print("\nOutlook邮箱特殊说明:")
                print("- 可能需要使用应用专用密码")
                print("- 设置地址: https://account.microsoft.com/security")
                
            elif '163.com' in domain or '126.com' in domain:
                print("\n网易邮箱特殊说明:")
                print("- 需要在邮箱设置中开启IMAP服务")
                print("- 使用授权码代替邮箱密码")
            
            print("=" * 60)
            
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

def test_multiple_accounts():
    """批量测试多个邮箱账户"""
    print("=" * 60)
    print("批量IMAP连接测试")
    print("=" * 60)
    
    print("请输入邮箱账户信息，格式: 邮箱地址,密码,服务器,端口")
    print("每行一个账户，输入空行结束")
    print("示例: <EMAIL>,password,imap.gmail.com,993")
    
    accounts = []
    while True:
        line = input("账户信息: ").strip()
        if not line:
            break
        
        parts = line.split(',')
        if len(parts) >= 2:
            email = parts[0].strip()
            password = parts[1].strip()
            server = parts[2].strip() if len(parts) > 2 else None
            port = int(parts[3].strip()) if len(parts) > 3 else 993
            
            # 自动检测服务器
            if not server and '@' in email:
                domain = email.split('@')[1].lower()
                if 'gmail' in domain:
                    server = 'imap.gmail.com'
                elif 'qq.com' in domain:
                    server = 'imap.qq.com'
                elif 'outlook' in domain or 'hotmail' in domain or 'live' in domain:
                    server = 'outlook.office365.com'
                elif '163.com' in domain:
                    server = 'imap.163.com'
                elif '126.com' in domain:
                    server = 'imap.126.com'
                else:
                    print(f"无法自动检测服务器，跳过: {email}")
                    continue
            
            accounts.append({
                'email': email,
                'password': password,
                'server': server,
                'port': port
            })
        else:
            print("格式错误，跳过此行")
    
    if not accounts:
        print("没有有效的账户信息")
        return
    
    print(f"\n开始测试 {len(accounts)} 个账户...")
    
    results = []
    for i, account in enumerate(accounts, 1):
        print(f"\n[{i}/{len(accounts)}] 测试 {account['email']}")
        
        try:
            handler = IMAPMailHandler(
                account['server'], 
                account['email'], 
                account['password'], 
                True,  # 默认使用SSL
                account['port']
            )
            
            success = handler.connect()
            
            if success:
                print(f"  ✓ 连接成功")
                handler.close()
                results.append({
                    'email': account['email'],
                    'success': True,
                    'message': '连接成功'
                })
            else:
                print(f"  ✗ 连接失败: {handler.error}")
                results.append({
                    'email': account['email'],
                    'success': False,
                    'message': handler.error
                })
                
        except Exception as e:
            print(f"  ✗ 测试失败: {str(e)}")
            results.append({
                'email': account['email'],
                'success': False,
                'message': str(e)
            })
    
    # 显示汇总结果
    print("\n" + "=" * 60)
    print("批量测试结果汇总")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r['success'])
    fail_count = len(results) - success_count
    
    print(f"总计: {len(results)} 个账户")
    print(f"成功: {success_count} 个")
    print(f"失败: {fail_count} 个")
    
    if fail_count > 0:
        print("\n失败的账户:")
        for result in results:
            if not result['success']:
                print(f"  ✗ {result['email']}: {result['message']}")

def main():
    """主函数"""
    print("IMAP连接测试工具")
    print("用于测试和验证IMAP邮箱连接配置")
    
    print("\n请选择测试模式:")
    print("1. 单个账户测试")
    print("2. 批量账户测试")
    print("3. 连接诊断")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    try:
        if choice == '1':
            test_imap_connection()
        elif choice == '2':
            test_multiple_accounts()
        elif choice == '3':
            # 运行诊断工具
            os.system('python imap_connection_diagnostics.py')
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    main()
