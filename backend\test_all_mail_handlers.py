#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试所有邮箱处理器的增强连接功能
验证IMAP、Outlook、Gmail、QQ、Yahoo等邮箱的连接处理
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.email.imap import IMAPMailHandler
from utils.email.outlook import OutlookMailHandler
from utils.email.gmail import GmailHandler
from utils.email.qq import QQMailHandler
from utils.email.yahoo import YahooMailHandler
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_handler_connection(handler_class, handler_name, *args, **kwargs):
    """测试邮箱处理器连接"""
    print(f"\n{'='*60}")
    print(f"测试 {handler_name} 连接处理")
    print(f"{'='*60}")
    
    try:
        # 创建处理器实例
        handler = handler_class(*args, **kwargs)
        
        print(f"处理器类型: {handler_class.__name__}")
        print(f"服务器: {getattr(handler, 'server', 'N/A')}")
        print(f"端口: {getattr(handler, 'port', 'N/A')}")
        print(f"用户名: {getattr(handler, 'username', getattr(handler, 'email_address', 'N/A'))}")
        
        # 测试连接方法是否存在
        if hasattr(handler, 'connect'):
            print("✓ 具有增强的connect方法")
            
            # 测试连接（使用无效凭据，只测试连接逻辑）
            print("\n测试连接逻辑（使用测试凭据）...")
            try:
                # 这里会失败，但我们可以验证错误处理逻辑
                success = handler.connect()
                if success:
                    print("✓ 连接成功（意外）")
                    handler.close()
                else:
                    print(f"✗ 连接失败（预期）: {handler.error}")
                    
                    # 检查错误信息是否包含解决建议
                    if handler.error and "解决建议" in handler.error:
                        print("✓ 包含解决建议")
                    else:
                        print("⚠ 缺少解决建议")
                        
            except Exception as e:
                print(f"✗ 连接测试异常: {str(e)}")
        else:
            print("✗ 缺少增强的connect方法")
        
        # 测试错误处理方法
        if hasattr(handler, '_provide_login_suggestions') or hasattr(handler, f'_provide_{handler_name.lower()}_suggestions'):
            print("✓ 具有错误建议方法")
        else:
            print("⚠ 缺少错误建议方法")
        
        # 测试关闭方法
        if hasattr(handler, 'close'):
            print("✓ 具有close方法")
            handler.close()
        else:
            print("⚠ 缺少close方法")
            
        print(f"✓ {handler_name} 测试完成")
        return True
        
    except Exception as e:
        print(f"✗ {handler_name} 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_all_handlers():
    """测试所有邮箱处理器"""
    print("邮箱处理器增强连接功能测试")
    print("验证所有邮箱类型的连接处理、错误处理和重试机制")
    
    test_results = []
    
    # 测试IMAP处理器
    result = test_handler_connection(
        IMAPMailHandler, 
        "IMAP",
        "imap.gmail.com", 
        "<EMAIL>", 
        "test_password"
    )
    test_results.append(("IMAP", result))
    
    # 测试Gmail处理器
    result = test_handler_connection(
        GmailHandler,
        "Gmail", 
        "<EMAIL>", 
        "test_password"
    )
    test_results.append(("Gmail", result))
    
    # 测试QQ邮箱处理器
    result = test_handler_connection(
        QQMailHandler,
        "QQ", 
        "<EMAIL>", 
        "test_password"
    )
    test_results.append(("QQ", result))
    
    # 测试Yahoo处理器
    result = test_handler_connection(
        YahooMailHandler,
        "Yahoo", 
        "<EMAIL>", 
        "test_password"
    )
    test_results.append(("Yahoo", result))
    
    # 测试Outlook处理器
    result = test_handler_connection(
        OutlookMailHandler,
        "Outlook", 
        "<EMAIL>", 
        "test_access_token"
    )
    test_results.append(("Outlook", result))
    
    # 显示测试结果汇总
    print(f"\n{'='*80}")
    print("测试结果汇总")
    print(f"{'='*80}")
    
    success_count = sum(1 for _, result in test_results if result)
    total_count = len(test_results)
    
    print(f"总计测试: {total_count} 个处理器")
    print(f"测试通过: {success_count} 个")
    print(f"测试失败: {total_count - success_count} 个")
    
    print(f"\n详细结果:")
    for handler_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {handler_name:10} : {status}")
    
    if success_count == total_count:
        print(f"\n🎉 所有邮箱处理器测试通过！")
    else:
        print(f"\n⚠ 有 {total_count - success_count} 个处理器测试失败，请检查实现。")

def test_error_handling():
    """测试错误处理机制"""
    print(f"\n{'='*80}")
    print("错误处理机制测试")
    print(f"{'='*80}")
    
    # 测试各种错误场景
    error_scenarios = [
        {
            'name': 'DNS解析失败',
            'handler': IMAPMailHandler,
            'args': ('nonexistent.server.com', '<EMAIL>', 'password'),
            'expected_error': 'DNS'
        },
        {
            'name': '连接被拒绝',
            'handler': IMAPMailHandler,
            'args': ('127.0.0.1', '<EMAIL>', 'password'),
            'kwargs': {'port': 12345},
            'expected_error': '拒绝'
        },
        {
            'name': '认证失败',
            'handler': GmailHandler,
            'args': ('<EMAIL>', 'wrong_password'),
            'expected_error': '认证'
        }
    ]
    
    for scenario in error_scenarios:
        print(f"\n测试场景: {scenario['name']}")
        print("-" * 40)
        
        try:
            args = scenario.get('args', ())
            kwargs = scenario.get('kwargs', {})
            handler = scenario['handler'](*args, **kwargs)
            
            # 尝试连接（预期失败）
            success = handler.connect(max_retries=1, retry_delay=1)  # 减少重试次数以加快测试
            
            if not success and handler.error:
                print(f"✓ 正确捕获错误: {handler.error[:100]}...")
                
                # 检查是否包含预期的错误类型
                if scenario['expected_error'].lower() in handler.error.lower():
                    print(f"✓ 错误类型匹配: {scenario['expected_error']}")
                else:
                    print(f"⚠ 错误类型不匹配，预期: {scenario['expected_error']}")
                
                # 检查是否包含解决建议
                if "解决建议" in handler.error:
                    print("✓ 包含解决建议")
                else:
                    print("⚠ 缺少解决建议")
            else:
                print(f"✗ 未正确处理错误场景")
                
        except Exception as e:
            print(f"✗ 测试异常: {str(e)}")

def test_connection_features():
    """测试连接功能特性"""
    print(f"\n{'='*80}")
    print("连接功能特性测试")
    print(f"{'='*80}")
    
    features_to_test = [
        "重试机制",
        "指数退避",
        "错误分类",
        "用户名格式尝试",
        "解决建议提供",
        "连接超时处理",
        "SSL错误处理"
    ]
    
    print("增强连接处理包含的功能特性:")
    for i, feature in enumerate(features_to_test, 1):
        print(f"  {i}. {feature}")
    
    print(f"\n这些功能已在以下邮箱处理器中实现:")
    handlers = [
        "IMAPMailHandler",
        "GmailHandler", 
        "QQMailHandler",
        "YahooMailHandler",
        "OutlookMailHandler"
    ]
    
    for handler in handlers:
        print(f"  ✓ {handler}")

def main():
    """主函数"""
    print("邮箱处理器增强连接功能测试套件")
    print("测试所有邮箱类型的连接处理、错误处理和重试机制")
    
    try:
        # 测试所有处理器
        test_all_handlers()
        
        # 测试错误处理
        test_error_handling()
        
        # 测试功能特性
        test_connection_features()
        
        print(f"\n{'='*80}")
        print("测试套件执行完成！")
        print("所有邮箱处理器现在都具有统一的增强连接处理能力。")
        print(f"{'='*80}")
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
