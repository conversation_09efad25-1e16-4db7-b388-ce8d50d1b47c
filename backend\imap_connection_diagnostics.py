#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IMAP连接诊断工具
专门用于诊断和解决"IMAP4rev1 Server logging out"错误
"""

import sys
import os
import imaplib
import socket
import ssl
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging

# 配置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IMAPDiagnostics:
    """IMAP连接诊断类"""
    
    def __init__(self):
        # 启用IMAP调试模式
        imaplib.Debug = 4
        
    def diagnose_connection(self, server, email, password, port=993, use_ssl=True):
        """全面诊断IMAP连接问题"""
        print("=" * 80)
        print("IMAP连接诊断开始")
        print("=" * 80)
        
        print(f"服务器: {server}")
        print(f"端口: {port}")
        print(f"SSL: {use_ssl}")
        print(f"邮箱: {email}")
        print(f"密码: {'*' * len(password)}")
        
        # 1. 网络连通性测试
        print("\n1. 网络连通性测试")
        if not self._test_network_connectivity(server, port):
            return False
            
        # 2. SSL/TLS测试
        if use_ssl:
            print("\n2. SSL/TLS连接测试")
            if not self._test_ssl_connection(server, port):
                return False
        
        # 3. IMAP服务器能力测试
        print("\n3. IMAP服务器能力测试")
        capabilities = self._test_imap_capabilities(server, port, use_ssl)
        if not capabilities:
            return False
            
        # 4. 认证方法测试
        print("\n4. 认证方法测试")
        auth_methods = self._extract_auth_methods(capabilities)
        print(f"支持的认证方法: {auth_methods}")
        
        # 5. 登录测试
        print("\n5. 登录测试")
        return self._test_login_methods(server, port, use_ssl, email, password, auth_methods)
    
    def _test_network_connectivity(self, server, port):
        """测试网络连通性"""
        try:
            print(f"测试连接到 {server}:{port}")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((server, port))
            sock.close()
            
            if result == 0:
                print("✓ 网络连接正常")
                return True
            else:
                print(f"✗ 网络连接失败，错误代码: {result}")
                return False
                
        except Exception as e:
            print(f"✗ 网络连接测试失败: {str(e)}")
            return False
    
    def _test_ssl_connection(self, server, port):
        """测试SSL连接"""
        try:
            print(f"测试SSL连接到 {server}:{port}")
            
            # 创建SSL上下文
            context = ssl.create_default_context()
            
            # 尝试连接
            with socket.create_connection((server, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=server) as ssock:
                    print(f"✓ SSL连接成功")
                    print(f"  SSL版本: {ssock.version()}")
                    print(f"  加密套件: {ssock.cipher()}")
                    return True
                    
        except Exception as e:
            print(f"✗ SSL连接失败: {str(e)}")
            
            # 尝试不验证证书的连接
            try:
                print("尝试不验证证书的SSL连接...")
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                with socket.create_connection((server, port), timeout=10) as sock:
                    with context.wrap_socket(sock, server_hostname=server) as ssock:
                        print("⚠ SSL连接成功（未验证证书）")
                        print("  建议检查服务器证书配置")
                        return True
                        
            except Exception as e2:
                print(f"✗ SSL连接完全失败: {str(e2)}")
                return False
    
    def _test_imap_capabilities(self, server, port, use_ssl):
        """测试IMAP服务器能力"""
        try:
            print(f"连接IMAP服务器获取能力信息")
            
            if use_ssl:
                mail = imaplib.IMAP4_SSL(server, port)
            else:
                mail = imaplib.IMAP4(server, port)
            
            # 获取服务器能力
            typ, data = mail.capability()
            if typ == 'OK':
                capabilities = data[0].decode().split()
                print("✓ 服务器能力获取成功:")
                for cap in capabilities:
                    print(f"  - {cap}")
                
                mail.logout()
                return capabilities
            else:
                print(f"✗ 获取服务器能力失败: {data}")
                mail.logout()
                return None
                
        except Exception as e:
            print(f"✗ IMAP能力测试失败: {str(e)}")
            return None
    
    def _extract_auth_methods(self, capabilities):
        """提取支持的认证方法"""
        auth_methods = []
        if capabilities:
            for cap in capabilities:
                if cap.startswith('AUTH='):
                    auth_methods.append(cap[5:])
        
        # 默认支持PLAIN登录
        if not auth_methods:
            auth_methods = ['PLAIN']
            
        return auth_methods
    
    def _test_login_methods(self, server, port, use_ssl, email, password, auth_methods):
        """测试不同的登录方法"""
        
        # 1. 标准登录测试
        print("\n5.1 标准LOGIN命令测试")
        if self._test_standard_login(server, port, use_ssl, email, password):
            return True
        
        # 2. 如果支持PLAIN认证，尝试PLAIN认证
        if 'PLAIN' in auth_methods:
            print("\n5.2 PLAIN认证测试")
            if self._test_plain_auth(server, port, use_ssl, email, password):
                return True
        
        # 3. 尝试不同的用户名格式
        print("\n5.3 不同用户名格式测试")
        return self._test_username_variations(server, port, use_ssl, email, password)
    
    def _test_standard_login(self, server, port, use_ssl, email, password):
        """测试标准LOGIN命令"""
        try:
            print(f"使用LOGIN命令登录: {email}")
            
            if use_ssl:
                mail = imaplib.IMAP4_SSL(server, port)
            else:
                mail = imaplib.IMAP4(server, port)
            
            # 尝试登录
            typ, data = mail.login(email, password)
            
            if typ == 'OK':
                print("✓ 标准LOGIN登录成功")
                mail.logout()
                return True
            else:
                print(f"✗ 标准LOGIN登录失败: {data}")
                mail.logout()
                return False
                
        except imaplib.IMAP4.error as e:
            print(f"✗ IMAP错误: {str(e)}")
            if "Server logging out" in str(e):
                print("  这通常表示认证失败或服务器拒绝连接")
                print("  可能的原因:")
                print("  - 用户名或密码错误")
                print("  - 需要应用专用密码")
                print("  - 服务器要求特定的认证方式")
                print("  - IP地址被限制")
            return False
        except Exception as e:
            print(f"✗ 连接错误: {str(e)}")
            return False
    
    def _test_plain_auth(self, server, port, use_ssl, email, password):
        """测试PLAIN认证"""
        try:
            print(f"使用PLAIN认证登录: {email}")
            
            if use_ssl:
                mail = imaplib.IMAP4_SSL(server, port)
            else:
                mail = imaplib.IMAP4(server, port)
            
            # 构建PLAIN认证字符串
            import base64
            auth_string = f'\0{email}\0{password}'
            auth_string_b64 = base64.b64encode(auth_string.encode()).decode()
            
            # 尝试PLAIN认证
            typ, data = mail.authenticate('PLAIN', lambda x: auth_string_b64)
            
            if typ == 'OK':
                print("✓ PLAIN认证登录成功")
                mail.logout()
                return True
            else:
                print(f"✗ PLAIN认证登录失败: {data}")
                mail.logout()
                return False
                
        except Exception as e:
            print(f"✗ PLAIN认证失败: {str(e)}")
            return False
    
    def _test_username_variations(self, server, port, use_ssl, email, password):
        """测试不同的用户名格式"""
        
        # 提取用户名部分
        if '@' in email:
            username = email.split('@')[0]
        else:
            username = email
        
        variations = [
            email,  # 完整邮箱地址
            username,  # 只有用户名部分
        ]
        
        for variation in variations:
            print(f"\n尝试用户名格式: {variation}")
            try:
                if use_ssl:
                    mail = imaplib.IMAP4_SSL(server, port)
                else:
                    mail = imaplib.IMAP4(server, port)
                
                typ, data = mail.login(variation, password)
                
                if typ == 'OK':
                    print(f"✓ 用户名格式 '{variation}' 登录成功")
                    mail.logout()
                    return True
                else:
                    print(f"✗ 用户名格式 '{variation}' 登录失败: {data}")
                    mail.logout()
                    
            except Exception as e:
                print(f"✗ 用户名格式 '{variation}' 测试失败: {str(e)}")
        
        return False
    
    def get_server_suggestions(self, email):
        """根据邮箱地址提供服务器配置建议"""
        if '@' not in email:
            return None
            
        domain = email.split('@')[1].lower()
        
        suggestions = {
            'gmail.com': {
                'server': 'imap.gmail.com',
                'port': 993,
                'use_ssl': True,
                'notes': '需要开启"不够安全的应用的访问权限"或使用应用专用密码'
            },
            'qq.com': {
                'server': 'imap.qq.com',
                'port': 993,
                'use_ssl': True,
                'notes': '需要在QQ邮箱设置中开启IMAP服务并获取授权码'
            },
            'outlook.com': {
                'server': 'outlook.office365.com',
                'port': 993,
                'use_ssl': True,
                'notes': '可能需要应用专用密码'
            },
            'hotmail.com': {
                'server': 'outlook.office365.com',
                'port': 993,
                'use_ssl': True,
                'notes': '可能需要应用专用密码'
            },
            '163.com': {
                'server': 'imap.163.com',
                'port': 993,
                'use_ssl': True,
                'notes': '需要在邮箱设置中开启IMAP服务并使用授权码'
            },
            '126.com': {
                'server': 'imap.126.com',
                'port': 993,
                'use_ssl': True,
                'notes': '需要在邮箱设置中开启IMAP服务并使用授权码'
            }
        }
        
        return suggestions.get(domain)

def main():
    """主函数"""
    print("IMAP连接诊断工具")
    print("专门用于诊断和解决'IMAP4rev1 Server logging out'错误")
    
    # 获取用户输入
    server = input("请输入IMAP服务器地址: ").strip()
    email = input("请输入邮箱地址: ").strip()
    password = input("请输入密码: ").strip()
    
    port = input("请输入端口号 (默认993): ").strip()
    if not port:
        port = 993
    else:
        port = int(port)
    
    use_ssl_input = input("是否使用SSL (Y/n): ").strip().lower()
    use_ssl = use_ssl_input != 'n'
    
    # 创建诊断实例
    diagnostics = IMAPDiagnostics()
    
    # 显示服务器建议
    suggestion = diagnostics.get_server_suggestions(email)
    if suggestion:
        print(f"\n建议配置:")
        print(f"服务器: {suggestion['server']}")
        print(f"端口: {suggestion['port']}")
        print(f"SSL: {suggestion['use_ssl']}")
        print(f"注意事项: {suggestion['notes']}")
        
        use_suggestion = input("\n是否使用建议配置? (Y/n): ").strip().lower()
        if use_suggestion != 'n':
            server = suggestion['server']
            port = suggestion['port']
            use_ssl = suggestion['use_ssl']
    
    # 开始诊断
    try:
        success = diagnostics.diagnose_connection(server, email, password, port, use_ssl)
        
        print("\n" + "=" * 80)
        if success:
            print("✓ 诊断完成：IMAP连接成功！")
        else:
            print("✗ 诊断完成：IMAP连接失败")
            print("\n建议检查:")
            print("1. 邮箱地址和密码是否正确")
            print("2. 是否需要使用应用专用密码或授权码")
            print("3. 是否需要在邮箱设置中开启IMAP服务")
            print("4. 服务器地址和端口是否正确")
            print("5. 网络连接是否正常")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print("\n\n诊断被用户中断")
    except Exception as e:
        print(f"\n\n诊断过程中出现错误: {str(e)}")

if __name__ == "__main__":
    main()
